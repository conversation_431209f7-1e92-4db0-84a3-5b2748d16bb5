const { test, expect } = require('@playwright/test');

test.describe('Ranking Page Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the ranking page
    await page.goto('http://localhost:3000/rankings/index.html');
  });

  test('should load ranking page successfully', async ({ page }) => {
    // Check page title
    await expect(page).toHaveTitle(/Bảng Xếp Hạng/);
    
    // Check main elements
    await expect(page.locator('.page-header')).toBeVisible();
    await expect(page.locator('h1:has-text("Bảng Xếp Hạng <PERSON>")')).toBeVisible();
    await expect(page.locator('#leaderboardContainer')).toBeVisible();
  });

  test('should display navigation correctly', async ({ page }) => {
    // Check navigation structure
    await expect(page.locator('header')).toBeVisible();
    await expect(page.locator('.logo img')).toBeVisible();
    await expect(page.locator('nav ul')).toBeVisible();
    
    // Check navigation links
    const navLinks = [
      'Trang Chủ',
      'Lớp Học', 
      'Thành Tích',
      'Đăng Ký',
      'Bảng Xếp Hạng',
      'Nghiên Cứu',
      'Tài Khoản'
    ];
    
    for (const linkText of navLinks) {
      await expect(page.locator(`nav a:has-text("${linkText}")`)).toBeVisible();
    }
    
    // Check active link
    await expect(page.locator('nav a.active:has-text("Bảng Xếp Hạng")')).toBeVisible();
  });

  test('should display footer correctly', async ({ page }) => {
    // Check footer structure
    await expect(page.locator('footer')).toBeVisible();
    await expect(page.locator('.footer-content')).toBeVisible();
    await expect(page.locator('.footer-logo')).toBeVisible();
    await expect(page.locator('.footer-contact')).toBeVisible();
    await expect(page.locator('.footer-social')).toBeVisible();
    
    // Check footer content
    await expect(page.locator('footer:has-text("Vthon Academy")')).toBeVisible();
    await expect(page.locator('footer:has-text("Học, học nữa, học mãi")')).toBeVisible();
    await expect(page.locator('footer:has-text("<EMAIL>")')).toBeVisible();
    await expect(page.locator('footer:has-text("0399787678")')).toBeVisible();
  });

  test('should handle empty ranking gracefully', async ({ page }) => {
    // Wait for page to load
    await page.waitForTimeout(3000);
    
    // Check if there are rankings or empty state
    const hasRankings = await page.locator('.leaderboard-item').count() > 0;
    const hasEmptyState = await page.locator(':has-text("Chưa có dữ liệu xếp hạng")').isVisible();
    
    expect(hasRankings || hasEmptyState).toBeTruthy();
    
    if (hasEmptyState) {
      // Check empty state elements
      await expect(page.locator(':has-text("Chưa có dữ liệu xếp hạng")')).toBeVisible();
      await expect(page.locator('a:has-text("Xem Lớp Học")')).toBeVisible();
      await expect(page.locator('a:has-text("Về Trang Chủ")')).toBeVisible();
    }
  });

  test('should display ranking data correctly when available', async ({ page }) => {
    // Wait for data to load
    await page.waitForTimeout(5000);
    
    const rankingItems = page.locator('.leaderboard-item');
    const itemCount = await rankingItems.count();
    
    if (itemCount > 0) {
      // Check first ranking item structure
      const firstItem = rankingItems.first();
      
      // Check rank badge
      await expect(firstItem.locator('.rank-badge')).toBeVisible();
      
      // Check student info
      await expect(firstItem.locator('.student-info')).toBeVisible();
      await expect(firstItem.locator('.student-avatar')).toBeVisible();
      await expect(firstItem.locator('.student-avatar img')).toBeVisible();
      await expect(firstItem.locator('.student-name')).toBeVisible();
      await expect(firstItem.locator('.student-class')).toBeVisible();
      
      // Check score
      await expect(firstItem.locator('.score')).toBeVisible();
      
      // Check completion time
      await expect(firstItem.locator('.completion-time')).toBeVisible();
    }
  });

  test('should handle avatar loading errors gracefully', async ({ page }) => {
    // Wait for data to load
    await page.waitForTimeout(5000);
    
    const avatarImages = page.locator('.student-avatar img');
    const imageCount = await avatarImages.count();
    
    if (imageCount > 0) {
      // Check that all avatar images have proper error handling
      for (let i = 0; i < imageCount; i++) {
        const img = avatarImages.nth(i);
        
        // Check that image has onerror attribute for fallback
        const onError = await img.getAttribute('onerror');
        expect(onError).toContain('user.png');
        
        // Verify image is visible (either original or fallback)
        await expect(img).toBeVisible();
      }
    }
  });

  test('should display ranking header correctly', async ({ page }) => {
    // Wait for page to load
    await page.waitForTimeout(3000);
    
    // Check leaderboard header
    const header = page.locator('.leaderboard-header');
    if (await header.isVisible()) {
      await expect(header.locator(':has-text("Hạng")')).toBeVisible();
      await expect(header.locator(':has-text("Học Viên")')).toBeVisible();
      await expect(header.locator(':has-text("Điểm Tổng")')).toBeVisible();
      await expect(header.locator(':has-text("Thời Gian")')).toBeVisible();
    }
  });

  test('should show total students info when data available', async ({ page }) => {
    // Wait for data to load
    await page.waitForTimeout(5000);
    
    const totalInfo = page.locator('#totalStudentsInfo');
    const infoText = await totalInfo.textContent();
    
    if (infoText && infoText.trim() !== '') {
      await expect(totalInfo).toBeVisible();
      expect(infoText).toContain('Tổng số học viên');
    }
  });

  test('should handle pagination when needed', async ({ page }) => {
    // Wait for data to load
    await page.waitForTimeout(5000);
    
    const paginationContainer = page.locator('#paginationContainer');
    const isVisible = await paginationContainer.isVisible();
    
    if (isVisible) {
      // Check pagination elements
      await expect(page.locator('#prevBtn')).toBeVisible();
      await expect(page.locator('#nextBtn')).toBeVisible();
      await expect(page.locator('#paginationInfo')).toBeVisible();
      
      // Check pagination info format
      const paginationText = await page.locator('#paginationInfo').textContent();
      expect(paginationText).toMatch(/Trang \d+ \/ \d+/);
    }
  });

  test('should update last updated time', async ({ page }) => {
    // Wait for data to load
    await page.waitForTimeout(5000);
    
    const lastUpdated = page.locator('#lastUpdated');
    const hasContent = await lastUpdated.textContent();
    
    if (hasContent && hasContent.trim() !== '') {
      await expect(lastUpdated).toBeVisible();
      expect(hasContent).toContain('Cập nhật lần cuối');
    }
  });

  test('should not have JavaScript errors', async ({ page }) => {
    // Monitor console for errors
    const consoleErrors = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });
    
    // Wait for page to fully load
    await page.waitForTimeout(5000);
    
    // Filter out Firebase permission errors (expected)
    const criticalErrors = consoleErrors.filter(msg => 
      !msg.includes('Missing or insufficient permissions') &&
      !msg.includes('FirebaseError') &&
      (msg.includes('TypeError') || 
       msg.includes('ReferenceError') ||
       msg.includes('SyntaxError'))
    );
    
    expect(criticalErrors.length).toBe(0);
  });
});
