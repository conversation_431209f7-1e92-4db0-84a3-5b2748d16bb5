// Firebase imports
import { initializeApp } from 'https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js';
import { getAuth, onAuthStateChanged, signOut } from 'https://www.gstatic.com/firebasejs/11.8.1/firebase-auth.js';
import { getFirestore, collection, addDoc, getDocs, query, where, serverTimestamp } from 'https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.js';

// Firebase configuration
const firebaseConfig = {
    apiKey: "AIzaSyBKNo8y_MOKYc3f3UdNRFwcMgeLRW71WXA",
    authDomain: "classroom-web-48bc2.firebaseapp.com",
    projectId: "classroom-web-48bc2",
    storageBucket: "classroom-web-48bc2.firebasestorage.app",
    messagingSenderId: "446746787502",
    appId: "1:446746787502:web:48d5ffd5a0b2c6e043b73f",
    measurementId: "G-742XRP9E96"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const db = getFirestore(app);

// Check authentication and admin access
onAuthStateChanged(auth, (user) => {
    if (user) {
        // Check if user is admin
        if (user.email !== '<EMAIL>') {
            alert('Bạn không có quyền truy cập trang này!');
            window.location.href = '../../index.html';
            return;
        }
        initializeMailbox();
    } else {
        // Redirect to login if not authenticated
        window.location.href = '../../auth/index.html';
    }
});

// Initialize mailbox functionality
function initializeMailbox() {
    setupEventListeners();
    updatePreview();
}

// Setup event listeners
function setupEventListeners() {
    // Logout button
    document.getElementById('logoutBtn').addEventListener('click', async () => {
        try {
            await signOut(auth);
            window.location.href = '../../index.html';
        } catch (error) {
            console.error('Error signing out:', error);
        }
    });

    // Recipient type selection
    document.querySelectorAll('.recipient-option').forEach(option => {
        option.addEventListener('click', () => {
            // Remove selected class from all options
            document.querySelectorAll('.recipient-option').forEach(opt => {
                opt.classList.remove('selected');
            });
            
            // Add selected class to clicked option
            option.classList.add('selected');
            
            // Show/hide relevant selectors
            const type = option.dataset.type;
            const classSelector = document.getElementById('classSelector');
            const emailSelector = document.getElementById('emailSelector');
            
            classSelector.style.display = type === 'class' ? 'block' : 'none';
            emailSelector.style.display = type === 'individual' ? 'block' : 'none';
        });
    });

    // Form inputs for preview update
    document.getElementById('senderName').addEventListener('input', updatePreview);
    document.getElementById('messageTitle').addEventListener('input', updatePreview);
    document.getElementById('messageContent').addEventListener('input', updatePreview);

    // Form submission
    document.getElementById('messageForm').addEventListener('submit', handleFormSubmit);
}

// Update preview
function updatePreview() {
    const senderName = document.getElementById('senderName').value || 'Admin Vthon Academy';
    const title = document.getElementById('messageTitle').value || 'Tiêu đề tin nhắn sẽ hiển thị ở đây';
    const content = document.getElementById('messageContent').value || 'Nội dung tin nhắn sẽ hiển thị ở đây...';

    document.getElementById('previewSender').textContent = senderName;
    document.getElementById('previewTitle').textContent = title;
    document.getElementById('previewContent').textContent = content;
    document.getElementById('previewAvatar').textContent = senderName.charAt(0).toUpperCase();
}

// Handle form submission
async function handleFormSubmit(e) {
    e.preventDefault();
    
    const submitButton = document.querySelector('.send-button');
    const loading = document.querySelector('.loading');
    const buttonText = document.querySelector('.button-text');
    
    // Show loading state
    submitButton.disabled = true;
    loading.classList.add('show');
    buttonText.textContent = 'Đang gửi...';
    
    try {
        const formData = getFormData();
        const recipients = await getRecipients(formData.recipientType, formData.targetClass, formData.targetEmail);
        
        if (recipients.length === 0) {
            throw new Error('Không tìm thấy người nhận nào!');
        }
        
        // Send messages to all recipients
        const promises = recipients.map(recipient => sendMessage(formData, recipient));
        await Promise.all(promises);
        
        showNotification(`Đã gửi thành công ${recipients.length} tin nhắn!`, 'success');
        
        // Reset form
        document.getElementById('messageForm').reset();
        updatePreview();
        
    } catch (error) {
        console.error('Error sending messages:', error);
        showNotification('Có lỗi xảy ra khi gửi tin nhắn: ' + error.message, 'error');
    } finally {
        // Hide loading state
        submitButton.disabled = false;
        loading.classList.remove('show');
        buttonText.textContent = 'Gửi Thông Báo';
    }
}

// Get form data
function getFormData() {
    const selectedRecipient = document.querySelector('.recipient-option.selected');
    return {
        senderName: document.getElementById('senderName').value,
        recipientType: selectedRecipient.dataset.type,
        targetClass: document.getElementById('targetClass').value,
        targetEmail: document.getElementById('targetEmail').value,
        title: document.getElementById('messageTitle').value,
        content: document.getElementById('messageContent').value
    };
}

// Get recipients based on type
async function getRecipients(type, targetClass, targetEmail) {
    const recipients = [];
    
    try {
        if (type === 'all') {
            // Get all users
            const usersSnapshot = await getDocs(collection(db, 'users'));
            usersSnapshot.forEach(doc => {
                const userData = doc.data();
                if (userData.email && userData.email !== '<EMAIL>') {
                    recipients.push({
                        email: userData.email,
                        name: userData.name || 'Học viên'
                    });
                }
            });
        } else if (type === 'class' && targetClass) {
            // Get users by class
            const usersSnapshot = await getDocs(
                query(collection(db, 'users'), where('selectedClass', '==', targetClass))
            );
            usersSnapshot.forEach(doc => {
                const userData = doc.data();
                if (userData.email && userData.email !== '<EMAIL>') {
                    recipients.push({
                        email: userData.email,
                        name: userData.name || 'Học viên'
                    });
                }
            });
        } else if (type === 'individual' && targetEmail) {
            // Get specific user
            const usersSnapshot = await getDocs(
                query(collection(db, 'users'), where('email', '==', targetEmail))
            );
            usersSnapshot.forEach(doc => {
                const userData = doc.data();
                recipients.push({
                    email: userData.email,
                    name: userData.name || 'Học viên'
                });
            });
        }
    } catch (error) {
        console.error('Error getting recipients:', error);
        throw new Error('Không thể lấy danh sách người nhận');
    }
    
    return recipients;
}

// Send message to a recipient
async function sendMessage(formData, recipient) {
    const messageData = {
        title: formData.title,
        content: formData.content,
        senderName: formData.senderName,
        senderEmail: '<EMAIL>',
        recipientEmail: recipient.email,
        recipientName: recipient.name,
        timestamp: serverTimestamp(),
        read: false,
        type: 'notification'
    };
    
    await addDoc(collection(db, 'messages'), messageData);
}

// Show notification
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#48bb78' : type === 'error' ? '#f56565' : '#4299e1'};
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 10000;
        font-weight: 500;
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Remove after 4 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 4000);
}
