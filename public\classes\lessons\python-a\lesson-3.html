<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bài 3: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> và Chuỗi - Python A</title>
    <link rel="stylesheet" href="../../../assets/css/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .lesson-container {
            max-width: 1000px;
            margin: 120px auto 50px;
            padding: 0 20px;
        }
        
        .lesson-header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: linear-gradient(135deg, #4285F4, #ff7aa8);
            color: white;
            border-radius: 15px;
        }
        
        .lesson-header h1 {
            font-size: 2.2rem;
            margin-bottom: 15px;
        }
        
        .lesson-header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .lesson-content {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            padding: 40px;
            margin-bottom: 30px;
        }
        
        .lesson-section {
            margin-bottom: 40px;
        }
        
        .lesson-section h2 {
            color: #4285F4;
            font-size: 1.8rem;
            margin-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 10px;
        }
        
        .lesson-section h3 {
            color: #333;
            font-size: 1.4rem;
            margin: 25px 0 15px;
        }
        
        .lesson-section ul {
            margin: 15px 0;
            padding-left: 25px;
        }
        
        .lesson-section li {
            margin-bottom: 8px;
            line-height: 1.6;
        }
        
        .lesson-section p {
            line-height: 1.7;
            margin-bottom: 15px;
        }
        
        .slide-link {
            display: inline-block;
            background: #4285F4;
            color: white;
            padding: 15px 25px;
            border-radius: 8px;
            text-decoration: none;
            margin: 20px 0;
            font-weight: 500;
            transition: background-color 0.3s;
        }
        
        .slide-link:hover {
            background: #3367D6;
            color: white;
        }
        
        .objectives-box {
            background: #f8f9fa;
            border-left: 4px solid #4285F4;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }
        
        .back-link {
            display: inline-block;
            color: #4285F4;
            text-decoration: none;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .back-link:hover {
            text-decoration: underline;
        }

        .assignments-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin-top: 30px;
        }

        .assignments-section h2 {
            color: #4285F4;
            text-align: center;
            margin-bottom: 30px;
        }

        .assignment-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .assignment-btn {
            display: block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            text-decoration: none;
            text-align: center;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .assignment-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .assignment-btn i {
            font-size: 2rem;
            margin-bottom: 10px;
            display: block;
        }

        .assignment-btn h3 {
            margin: 10px 0 5px 0;
            font-size: 1.3rem;
        }

        .assignment-btn p {
            margin: 0;
            opacity: 0.9;
            font-size: 0.95rem;
        }

        .code-example {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
        }

        .highlight {
            background: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <header>
        <div class="container">
            <div class="logo">
                <img src="../../../assets/images/logo.jpg" alt="VTA Logo">
            </div>
            <nav>
                <ul>
                    <li><a href="../../../index.html">Trang Chủ</a></li>
                    <li><a href="../../index.html">Lớp Học</a></li>
                    <li><a href="../../../achievements/">Thành Tích</a></li>
                    <li><a href="../../../auth/register.html">Đăng Ký</a></li>
                    <li><a href="../../../rankings/">Bảng Xếp Hạng</a></li>
                    <li><a href="../../../research/">Nghiên Cứu</a></li>
                    <li><a href="../../../auth/">Tài Khoản</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <div class="lesson-container">
        <a href="../../class-detail.html?class=python-a" class="back-link">
            <i class="fas fa-arrow-left"></i> Quay lại lớp Python - A
        </a>

        <div class="lesson-header">
            <h1>Bài 3: Dữ Liệu – Biến, Kiểu Số và Chuỗi</h1>
            <p>Khám phá thế giới dữ liệu trong Python</p>
        </div>

        <div class="lesson-content">
            <div class="lesson-section">
                <h2>Slide Bài Học</h2>
                <a href="https://www.canva.com/design/DAGqGqjOQdg/Skr50HWmwxHBIjNrgkUCtg/edit?utm_content=DAGqGqjOQdg&utm_campaign=designshare&utm_medium=link2&utm_source=sharebutton" 
                   target="_blank" class="slide-link">
                    <i class="fas fa-presentation"></i> Xem Slide Bài Học (Canva)
                </a>
            </div>

            <div class="lesson-section">
                <h2>Mục Tiêu Bài Học</h2>
                <div class="objectives-box">
                    <ul>
                        <li>Hiểu và sử dụng được biến (variables)</li>
                        <li>Nắm được các kiểu dữ liệu cơ bản: số nguyên (integer), số thực (float), chuỗi (string)</li>
                        <li>Thực hiện được các thao tác cơ bản với chuỗi</li>
                    </ul>
                </div>
                <p><strong>Thời lượng dự kiến:</strong> 1 buổi</p>
            </div>

            <div class="lesson-section">
                <h2>Nội Dung Chính</h2>
                
                <h3>1. Biến (Variables)</h3>
                <ul>
                    <li><strong>Khái niệm:</strong> Biến là "hộp chứa" để lưu trữ dữ liệu trong chương trình</li>
                    <li><strong>Quy tắc đặt tên biến:</strong>
                        <ul>
                            <li>Bắt đầu bằng chữ cái hoặc dấu gạch dưới (_)</li>
                            <li>Không chứa khoảng trắng, sử dụng dấu gạch dưới thay thế</li>
                            <li>Phân biệt chữ hoa và chữ thường</li>
                            <li>Không được trùng với từ khóa của Python</li>
                        </ul>
                    </li>
                    <li><strong>Phép gán:</strong> Sử dụng dấu <span class="highlight">=</span> để gán giá trị cho biến</li>
                </ul>
                <div class="code-example">
# Ví dụ về biến<br>
ten_hoc_vien = "Nguyễn Văn A"<br>
tuoi = 15<br>
diem_toan = 8.5
                </div>

                <h3>2. Kiểu dữ liệu Số (Numbers)</h3>
                <ul>
                    <li><strong>Số nguyên (integer):</strong> Các số không có phần thập phân
                        <div class="code-example">
so_hoc_vien = 25<br>
nam_sinh = 2008
                        </div>
                    </li>
                    <li><strong>Số thực (float):</strong> Các số có phần thập phân
                        <div class="code-example">
diem_trung_binh = 8.75<br>
chieu_cao = 1.65
                        </div>
                    </li>
                </ul>

                <h3>3. Kiểu dữ liệu Chuỗi (String)</h3>
                <ul>
                    <li><strong>Khái niệm:</strong> Chuỗi là dãy các ký tự được bao quanh bởi dấu nháy</li>
                    <li><strong>Cách tạo chuỗi:</strong>
                        <ul>
                            <li>Dấu nháy đơn: <span class="highlight">'Hello'</span></li>
                            <li>Dấu nháy kép: <span class="highlight">"Hello"</span></li>
                            <li>Ba dấu nháy: <span class="highlight">"""Hello"""</span> (cho chuỗi nhiều dòng)</li>
                        </ul>
                    </li>
                </ul>
                <div class="code-example">
ten = "Vthon Academy"<br>
slogan = 'Học, học nữa, học mãi'<br>
mo_ta = """Đây là một chuỗi<br>
có nhiều dòng"""
                </div>

                <h3>4. Các thao tác cơ bản với chuỗi</h3>
                <ul>
                    <li><strong>Nối chuỗi (+):</strong> <span class="highlight">"Hello" + " World"</span></li>
                    <li><strong>Lặp chuỗi (*):</strong> <span class="highlight">"Ha" * 3</span> → "HaHaHa"</li>
                    <li><strong>Độ dài chuỗi:</strong> <span class="highlight">len("Hello")</span> → 5</li>
                    <li><strong>Truy cập ký tự (indexing):</strong> <span class="highlight">"Hello"[0]</span> → "H"</li>
                    <li><strong>Cắt chuỗi (slicing):</strong> <span class="highlight">"Hello"[1:4]</span> → "ell"</li>
                </ul>

                <h3>5. Một số phương thức chuỗi cơ bản</h3>
                <ul>
                    <li><strong>upper():</strong> Chuyển thành chữ hoa</li>
                    <li><strong>lower():</strong> Chuyển thành chữ thường</li>
                    <li><strong>strip():</strong> Loại bỏ khoảng trắng đầu và cuối</li>
                    <li><strong>find():</strong> Tìm vị trí của chuỗi con</li>
                    <li><strong>replace():</strong> Thay thế chuỗi con</li>
                </ul>
                <div class="code-example">
text = "  Vthon Academy  "<br>
print(text.upper())     # "  VTHON ACADEMY  "<br>
print(text.strip())     # "Vthon Academy"<br>
print(text.replace("Academy", "School"))  # "  Vthon School  "
                </div>
            </div>
        </div>

        <!-- Assignments Section -->
        <div class="assignments-section">
            <h2><i class="fas fa-tasks"></i> Bài Tập Thực Hành</h2>
            <p style="text-align: center; color: #666; margin-bottom: 30px;">
                Hoàn thành bài tập trắc nghiệm để kiểm tra kiến thức về biến, số và chuỗi
            </p>

            <div class="assignment-buttons">
                <a href="../../assignments/python-a/lesson-3-quiz.html" class="assignment-btn">
                    <i class="fas fa-question-circle"></i>
                    <h3>Bài Tập Trắc Nghiệm</h3>
                    <p>50 câu hỏi trắc nghiệm - 100 điểm</p>
                </a>

                <a href="../../assignments/python-a/lesson-3-code.html" class="assignment-btn" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                    <i class="fas fa-code"></i>
                    <h3>Bài Tập Luyện Tập</h3>
                    <p>17 bài tập code để luyện tập (không tính điểm)</p>
                </a>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section footer-logo">
                    <div class="footer-logo-container">
                        <img src="../../../assets/images/logo.jpg" alt="VTA Logo">
                        <div class="logo-text">Vthon Academy</div>
                    </div>
                    <div class="slogan">Học, học nữa, học mãi.</div>
                    <div class="footer-bottom">
                        <p>&copy; 2025 – All rights reserved.</p>
                    </div>
                </div>

                <div class="footer-section footer-contact">
                    <h3>Liên hệ</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> 0399787678</p>
                    <p><i class="fab fa-facebook-messenger"></i> Zalo: 0399787678</p>
                </div>

                <div class="footer-section footer-social">
                    <h3>Mạng xã hội</h3>
                    <div class="social-links">
                        <a href="https://www.facebook.com/vinhle030904/" target="_blank" class="social-link facebook">
                            <i class="fab fa-facebook-f"></i> Facebook
                        </a>
                        <a href="https://www.tiktok.com/@sunnii39" target="_blank" class="social-link tiktok">
                            <i class="fab fa-tiktok"></i> TikTok
                        </a>
                        <a href="https://www.instagram.com/vlee.39/?hl=en" target="_blank" class="social-link instagram">
                            <i class="fab fa-instagram"></i> Instagram
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script src="../../../assets/js/script.js"></script>
</body>
</html>
