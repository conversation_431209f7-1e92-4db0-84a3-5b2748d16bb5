const { test, expect } = require('@playwright/test');

test.describe('Profile Page Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the profile page
    await page.goto('http://localhost:3000/auth/profile.html');
  });

  test('should redirect to login if not authenticated', async ({ page }) => {
    // Wait for redirect to login page
    await page.waitForURL('**/auth/index.html', { timeout: 10000 });
    
    // Verify we're on the login page
    expect(page.url()).toContain('/auth/index.html');
  });

  test('should display profile page for authenticated user', async ({ page }) => {
    // First login with test account
    await page.goto('http://localhost:3000/auth/index.html');
    
    // Fill login form
    await page.fill('#loginEmail', '<EMAIL>');
    await page.fill('#loginPassword', '123456');
    
    // Click login button
    await page.click('#loginBtn');
    
    // Wait for login to complete
    await page.waitForTimeout(3000);
    
    // Navigate to profile page
    await page.goto('http://localhost:3000/auth/profile.html');
    
    // Wait for page to load
    await page.waitForTimeout(2000);
    
    // Check if profile elements are visible
    await expect(page.locator('.profile-container')).toBeVisible();
    await expect(page.locator('.profile-header')).toBeVisible();
    await expect(page.locator('#profileAvatarImg')).toBeVisible();
  });

  test('should have consistent navigation with other pages', async ({ page }) => {
    // Login first
    await page.goto('http://localhost:3000/auth/index.html');
    await page.fill('#loginEmail', '<EMAIL>');
    await page.fill('#loginPassword', '123456');
    await page.click('#loginBtn');
    await page.waitForTimeout(3000);
    
    // Go to profile page
    await page.goto('http://localhost:3000/auth/profile.html');
    await page.waitForTimeout(2000);
    
    // Check navigation structure
    await expect(page.locator('header')).toBeVisible();
    await expect(page.locator('.logo img')).toBeVisible();
    await expect(page.locator('nav ul')).toBeVisible();
    
    // Check navigation links
    const navLinks = [
      'Trang Chủ',
      'Lớp Học', 
      'Thành Tích',
      'Đăng Ký',
      'Bảng Xếp Hạng',
      'Nghiên Cứu',
      'Tài Khoản'
    ];
    
    for (const linkText of navLinks) {
      await expect(page.locator(`nav a:has-text("${linkText}")`)).toBeVisible();
    }
  });

  test('should have consistent footer with other pages', async ({ page }) => {
    // Login first
    await page.goto('http://localhost:3000/auth/index.html');
    await page.fill('#loginEmail', '<EMAIL>');
    await page.fill('#loginPassword', '123456');
    await page.click('#loginBtn');
    await page.waitForTimeout(3000);
    
    // Go to profile page
    await page.goto('http://localhost:3000/auth/profile.html');
    await page.waitForTimeout(2000);
    
    // Check footer structure
    await expect(page.locator('footer')).toBeVisible();
    await expect(page.locator('.footer-content')).toBeVisible();
    await expect(page.locator('.footer-logo')).toBeVisible();
    await expect(page.locator('.footer-contact')).toBeVisible();
    await expect(page.locator('.footer-social')).toBeVisible();
    
    // Check footer content
    await expect(page.locator('footer:has-text("Vthon Academy")')).toBeVisible();
    await expect(page.locator('footer:has-text("Học, học nữa, học mãi")')).toBeVisible();
    await expect(page.locator('footer:has-text("<EMAIL>")')).toBeVisible();
    await expect(page.locator('footer:has-text("0399787678")')).toBeVisible();
  });

  test('should display inbox without errors', async ({ page }) => {
    // Login first
    await page.goto('http://localhost:3000/auth/index.html');
    await page.fill('#loginEmail', '<EMAIL>');
    await page.fill('#loginPassword', '123456');
    await page.click('#loginBtn');
    await page.waitForTimeout(3000);
    
    // Go to profile page
    await page.goto('http://localhost:3000/auth/profile.html');
    await page.waitForTimeout(3000);
    
    // Check inbox section
    await expect(page.locator('.profile-card:has-text("Hộp Thư")')).toBeVisible();
    await expect(page.locator('#inboxContent')).toBeVisible();
    
    // Should show either messages or empty inbox message
    const hasMessages = await page.locator('.inbox-item').count() > 0;
    const hasEmptyMessage = await page.locator('.empty-inbox').isVisible();
    
    expect(hasMessages || hasEmptyMessage).toBeTruthy();
  });

  test('should display achievements section', async ({ page }) => {
    // Login first
    await page.goto('http://localhost:3000/auth/index.html');
    await page.fill('#loginEmail', '<EMAIL>');
    await page.fill('#loginPassword', '123456');
    await page.click('#loginBtn');
    await page.waitForTimeout(3000);
    
    // Go to profile page
    await page.goto('http://localhost:3000/auth/profile.html');
    await page.waitForTimeout(3000);
    
    // Check achievements section
    await expect(page.locator('.profile-card:has-text("Huy Hiệu & Thành Tích")')).toBeVisible();
    await expect(page.locator('#achievementsGrid')).toBeVisible();
  });

  test('should not show notification badge when no unread messages', async ({ page }) => {
    // Login first
    await page.goto('http://localhost:3000/auth/index.html');
    await page.fill('#loginEmail', '<EMAIL>');
    await page.fill('#loginPassword', '123456');
    await page.click('#loginBtn');
    await page.waitForTimeout(3000);
    
    // Go to profile page
    await page.goto('http://localhost:3000/auth/profile.html');
    await page.waitForTimeout(3000);
    
    // Check that notification badge is hidden (since we don't have messages collection set up)
    const badge = page.locator('#inboxNotificationBadge');
    await expect(badge).toHaveCSS('display', 'none');
  });

  test('should handle Firebase errors gracefully', async ({ page }) => {
    // Monitor console for errors
    const consoleMessages = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleMessages.push(msg.text());
      }
    });
    
    // Login first
    await page.goto('http://localhost:3000/auth/index.html');
    await page.fill('#loginEmail', '<EMAIL>');
    await page.fill('#loginPassword', '123456');
    await page.click('#loginBtn');
    await page.waitForTimeout(3000);
    
    // Go to profile page
    await page.goto('http://localhost:3000/auth/profile.html');
    await page.waitForTimeout(5000);
    
    // Page should still load despite Firebase permission errors
    await expect(page.locator('.profile-container')).toBeVisible();
    
    // Should not have critical JavaScript errors that break the page
    const criticalErrors = consoleMessages.filter(msg => 
      msg.includes('TypeError') || 
      msg.includes('ReferenceError') ||
      msg.includes('SyntaxError')
    );
    
    expect(criticalErrors.length).toBe(0);
  });
});
