<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bảng Xếp <PERSON> - <PERSON>thon</title>
    <link rel="stylesheet" href="../assets/css/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .page-header {
            text-align: center;
            margin: 120px 0 50px;
        }
        
        .page-header h1 {
            font-size: 2.5rem;
            color: #333;
            margin-bottom: 15px;
        }
        
        .page-header p {
            color: #666;
            font-size: 1.2rem;
            max-width: 700px;
            margin: 0 auto;
        }
        
        .leaderboard-container {
            max-width: 800px;
            margin: 0 auto 50px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .leaderboard-header {
            background-color: #111;
            color: white;
            padding: 15px 20px;
            display: grid;
            grid-template-columns: 80px 1fr 120px 120px;
            align-items: center;
            font-weight: 500;
        }
        
        .leaderboard-item {
            padding: 20px;
            display: grid;
            grid-template-columns: 80px 1fr 120px 120px;
            align-items: center;
            border-bottom: 1px solid #eee;
            transition: background-color 0.3s;
        }
        
        .leaderboard-item:hover {
            background-color: #f8f9fa;
        }
        
        .leaderboard-item:last-child {
            border-bottom: none;
        }
        
        .rank {
            font-size: 1.5rem;
            font-weight: 700;
            color: #333;
            text-align: center;
        }
        
        .rank-1 {
            color: #FFD700; /* Gold */
        }
        
        .rank-2 {
            color: #C0C0C0; /* Silver */
        }
        
        .rank-3 {
            color: #CD7F32; /* Bronze */
        }
        
        .student-info {
            display: flex;
            align-items: center;
        }
        
        .student-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 15px;
            border: 2px solid #fff;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
        
        .student-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .student-name {
            font-weight: 500;
            color: #333;
            margin-bottom: 5px;
        }
        
        .student-class {
            color: #666;
            font-size: 0.9rem;
        }
        
        .score {
            font-weight: 700;
            color: #4285F4;
            font-size: 1.2rem;
            text-align: center;
        }
        
        .leaderboard-item:nth-child(odd) {
            background-color: #f8f9fa;
        }
        
        .leaderboard-item:nth-child(odd):hover {
            background-color: #f1f3f5;
        }
        
        .rank-badge {
            display: inline-flex;
            justify-content: center;
            align-items: center;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            font-weight: 700;
            color: white;
        }
        
        .rank-badge-1 {
            background-color: #FFD700;
            box-shadow: 0 3px 10px rgba(255, 215, 0, 0.3);
        }
        
        .rank-badge-2 {
            background-color: #C0C0C0;
            box-shadow: 0 3px 10px rgba(192, 192, 192, 0.3);
        }
        
        .rank-badge-3 {
            background-color: #CD7F32;
            box-shadow: 0 3px 10px rgba(205, 127, 50, 0.3);
        }
        
        .rank-badge-other {
            background-color: #4285F4;
            box-shadow: 0 3px 10px rgba(66, 133, 244, 0.3);
        }
        
        .last-updated {
            text-align: center;
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 50px;
            font-style: italic;
        }

        .pagination-container {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 30px 0;
            gap: 20px;
        }

        .pagination-btn {
            background: #4285F4;
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1.2rem;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 50px;
            height: 50px;
        }

        .pagination-btn:hover:not(:disabled) {
            background: #3367D6;
            transform: translateY(-2px);
        }

        .pagination-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        .pagination-info {
            background: white;
            padding: 10px 20px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            font-weight: 500;
            color: #333;
        }

        .total-students {
            text-align: center;
            margin: 20px 0;
            color: #666;
            font-size: 1rem;
        }
        
        @media (max-width: 768px) {
            .leaderboard-header,
            .leaderboard-item {
                grid-template-columns: 50px 1fr 70px 70px;
                padding: 15px 10px;
            }
            
            .student-avatar {
                width: 40px;
                height: 40px;
                margin-right: 10px;
            }
            
            .student-name {
                font-size: 0.9rem;
            }
            
            .student-class {
                font-size: 0.8rem;
            }
            
            .score {
                font-size: 1rem;
            }
            
            .rank {
                font-size: 1.2rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <header>
        <div class="container">
            <div class="logo">
                <img src="../assets/images/logo.jpg" alt="VTA Logo">
            </div>
            <nav>
                <ul>
                    <li><a href="../index.html">Trang Chủ</a></li>
                    <li><a href="../classes/">Lớp Học</a></li>
                    <li><a href="../achievements/">Thành Tích</a></li>
                    <li><a href="../auth/register.html">Đăng Ký</a></li>
                    <li><a href="index.html" class="active">Bảng Xếp Hạng</a></li>
                    <li><a href="../research/">Nghiên Cứu</a></li>
                    <li><a href="../auth/">Tài Khoản</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Rankings Section -->
    <section>
        <div class="container">
            <div class="page-header">
                <h1>Bảng Xếp Hạng Học Viên</h1>
                <p>Bảng xếp hạng tất cả học viên đã tham gia làm bài tập dựa trên điểm tổng các bài kiểm tra. Khi điểm bằng nhau, học viên hoàn thành nhanh hơn sẽ xếp hạng cao hơn.</p>
            </div>

            <div class="total-students" id="totalStudentsInfo">
                <!-- Total students info will be displayed here -->
            </div>
            
            <div class="leaderboard-container" id="leaderboardContainer">
                <!-- Leaderboard content will be loaded here -->
            </div>

            <div class="pagination-container" id="paginationContainer" style="display: none;">
                <button class="pagination-btn" id="prevBtn" onclick="changePage(-1)">
                    ⭠
                </button>
                <div class="pagination-info" id="paginationInfo">
                    Trang 1 / 1
                </div>
                <button class="pagination-btn" id="nextBtn" onclick="changePage(1)">
                    ⮕
                </button>
            </div>
        </div>

        <div class="last-updated" id="lastUpdated">
            <!-- Last updated time will be displayed here -->
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section footer-logo">
                    <div class="footer-logo-container">
                        <img src="../assets/images/logo.jpg" alt="VTA Logo">
                        <div class="logo-text">Vthon Academy</div>
                    </div>
                    <div class="slogan">Học, học nữa, học mãi.</div>
                    <div class="footer-bottom">
                        <p>&copy; 2025 – All rights reserved.</p>
                    </div>
                </div>

                <div class="footer-section footer-contact">
                    <h3>Liên hệ</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> 0399787678</p>
                    <p><i class="fab fa-facebook-messenger"></i> Zalo: 0399787678</p>
                </div>

                <div class="footer-section footer-social">
                    <h3>Mạng xã hội</h3>
                    <div class="social-links">
                        <a href="https://www.facebook.com/vinhle030904/" target="_blank" class="social-link facebook">
                            <i class="fab fa-facebook-f"></i> Facebook
                        </a>
                        <a href="https://www.tiktok.com/@sunnii39" target="_blank" class="social-link tiktok">
                            <i class="fab fa-tiktok"></i> TikTok
                        </a>
                        <a href="https://www.instagram.com/vlee.39/?hl=en" target="_blank" class="social-link instagram">
                            <i class="fab fa-instagram"></i> Instagram
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script type="module">
        // Import Firebase modules
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js";
        import { getAuth, onAuthStateChanged } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-auth.js";
        import { getFirestore, collection, getDocs, query, orderBy, limit } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBKNo8y_MOKYc3f3UdNRFwcMgeLRW71WXA",
            authDomain: "classroom-web-48bc2.firebaseapp.com",
            projectId: "classroom-web-48bc2",
            storageBucket: "classroom-web-48bc2.firebasestorage.app",
            messagingSenderId: "446746787502",
            appId: "1:446746787502:web:48d5ffd5a0b2c6e043b73f",
            measurementId: "G-742XRP9E96"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);

        // Pagination variables
        let allRankings = [];
        let currentPage = 1;
        const itemsPerPage = 10;
        const adminEmail = '<EMAIL>';

        // Load real rankings from Firebase
        async function loadRankings() {
            try {
                // Get all users first, then filter and sort
                const querySnapshot = await getDocs(collection(db, "users"));
                const rankings = [];

                querySnapshot.forEach((doc) => {
                    const userData = doc.data();
                    // Exclude admin and include users who have completed profile and have done assignments
                    if (userData.fullName && userData.fullName.trim() !== '' && userData.email !== adminEmail) {
                        const totalScore = userData.totalScore || 0;
                        const assignmentCount = userData.assignmentCount || 0;
                        // Include users who have done at least one assignment (assignmentCount > 0)
                        if (assignmentCount > 0) {
                            rankings.push({
                                name: userData.fullName,
                                class: userData.courseClass || 'Chưa phân lớp',
                                score: totalScore,
                                avatar: userData.avatar || '../assets/images/user.png',
                                lastAssignmentDate: userData.lastAssignmentDate || null,
                                totalCompletionTime: userData.totalCompletionTime || 0,
                                email: userData.email,
                                assignmentCount: assignmentCount
                            });
                        }
                    }
                });

                // Sort by score descending, then by total completion time ascending (shorter time wins ties)
                rankings.sort((a, b) => {
                    if (b.score !== a.score) {
                        return b.score - a.score;
                    }
                    // If scores are equal, shorter completion time wins
                    if (a.totalCompletionTime && b.totalCompletionTime) {
                        return a.totalCompletionTime - b.totalCompletionTime;
                    }
                    // If one has completion time and other doesn't, prioritize the one with time
                    if (a.totalCompletionTime && !b.totalCompletionTime) return -1;
                    if (!a.totalCompletionTime && b.totalCompletionTime) return 1;
                    return 0;
                });

                // Store all rankings and reset to first page
                allRankings = rankings;
                currentPage = 1;

                // Update the leaderboard with pagination
                updateLeaderboard();
                updateTotalStudentsInfo();

            } catch (error) {
                console.error("Error loading rankings:", error);
                // Show error state
                allRankings = [];
                updateLeaderboard();
                updateTotalStudentsInfo();
            }
        }

        function updateLeaderboard() {
            const leaderboardContainer = document.getElementById('leaderboardContainer');
            const paginationContainer = document.getElementById('paginationContainer');

            // Calculate pagination
            const totalPages = Math.ceil(allRankings.length / itemsPerPage);
            const startIndex = (currentPage - 1) * itemsPerPage;
            const endIndex = startIndex + itemsPerPage;
            const currentRankings = allRankings.slice(startIndex, endIndex);

            // Keep the header
            let html = `
                <div class="leaderboard-header">
                    <div>Hạng</div>
                    <div>Học Viên</div>
                    <div>Điểm Tổng</div>
                    <div>Thời Gian</div>
                </div>
            `;

            if (allRankings.length === 0) {
                html += `
                    <div style="text-align: center; padding: 100px 20px; color: #666;">
                        <i class="fas fa-trophy" style="font-size: 4rem; margin-bottom: 30px; color: #ddd;"></i>
                        <h3 style="font-size: 2rem; margin-bottom: 20px; color: #999;">Chưa có dữ liệu xếp hạng</h3>
                        <p style="font-size: 1.2rem; max-width: 600px; margin: 0 auto; line-height: 1.6;">
                            Bảng xếp hạng sẽ được cập nhật khi có học viên tham gia làm các bài kiểm tra và bài tập.
                            Hãy tham gia các lớp học và làm bài tập để xuất hiện trên bảng xếp hạng!
                        </p>
                        <div style="margin-top: 30px;">
                            <a href="../classes/" style="display: inline-block; background: #4285F4; color: white; padding: 12px 25px; border-radius: 8px; text-decoration: none; font-weight: 500; margin-right: 15px; transition: background-color 0.3s;">
                                <i class="fas fa-book"></i> Xem Lớp Học
                            </a>
                            <a href="../index.html" style="display: inline-block; background: #28a745; color: white; padding: 12px 25px; border-radius: 8px; text-decoration: none; font-weight: 500; transition: background-color 0.3s;">
                                <i class="fas fa-home"></i> Về Trang Chủ
                            </a>
                        </div>
                    </div>
                `;
                paginationContainer.style.display = 'none';
            } else {
                currentRankings.forEach((student, index) => {
                    const globalRank = startIndex + index + 1; // Global rank across all pages
                    const rankClass = globalRank <= 3 ? `rank-${globalRank}` : 'rank-other';
                    const className = getClassDisplayName(student.class);

                    // Format completion time
                    const totalMinutes = Math.floor(student.totalCompletionTime / 60);
                    const totalSeconds = student.totalCompletionTime % 60;
                    const timeDisplay = student.totalCompletionTime > 0 ?
                        `${totalMinutes}:${totalSeconds.toString().padStart(2, '0')}` :
                        '--:--';

                    html += `
                        <div class="leaderboard-item">
                            <div class="rank">
                                <div class="rank-badge rank-badge-${globalRank <= 3 ? globalRank : 'other'}">${globalRank}</div>
                            </div>
                            <div class="student-info">
                                <div class="student-avatar">
                                    <img src="${student.avatar}" alt="${student.name}" onerror="this.src='../assets/images/user.png'">
                                </div>
                                <div>
                                    <div class="student-name">${student.name}</div>
                                    <div class="student-class">${className}</div>
                                </div>
                            </div>
                            <div class="score">${student.score}</div>
                            <div class="completion-time" style="text-align: center; font-weight: 500; color: #666;">${timeDisplay}</div>
                        </div>
                    `;
                });

                // Show pagination if there are multiple pages
                if (totalPages > 1) {
                    paginationContainer.style.display = 'flex';
                    updatePaginationControls(totalPages);
                } else {
                    paginationContainer.style.display = 'none';
                }
            }

            leaderboardContainer.innerHTML = html;

            // Update last updated time
            const lastUpdated = document.getElementById('lastUpdated');
            if (lastUpdated && allRankings.length > 0) {
                const now = new Date();
                lastUpdated.textContent = `Cập nhật lần cuối: ${now.toLocaleDateString('vi-VN')} lúc ${now.toLocaleTimeString('vi-VN')}`;
            }
        }

        function updatePaginationControls(totalPages) {
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');
            const paginationInfo = document.getElementById('paginationInfo');

            // Update pagination info
            paginationInfo.textContent = `Trang ${currentPage} / ${totalPages}`;

            // Update button states
            prevBtn.disabled = currentPage === 1;
            nextBtn.disabled = currentPage === totalPages;
        }

        function changePage(direction) {
            const totalPages = Math.ceil(allRankings.length / itemsPerPage);
            const newPage = currentPage + direction;

            if (newPage >= 1 && newPage <= totalPages) {
                currentPage = newPage;
                updateLeaderboard();
            }
        }

        function updateTotalStudentsInfo() {
            const totalStudentsInfo = document.getElementById('totalStudentsInfo');
            if (allRankings.length > 0) {
                totalStudentsInfo.innerHTML = `
                    <i class="fas fa-users" style="margin-right: 8px; color: #4285F4;"></i>
                    Tổng số học viên đã làm bài tập: <strong>${allRankings.length}</strong> học viên
                `;
            } else {
                totalStudentsInfo.innerHTML = '';
            }
        }

        function getClassDisplayName(classId) {
            const classNames = {
                'python-a': 'Python - A',
                'python-b': 'Python - B',
                'python-c': 'Python - C'
            };
            return classNames[classId] || classId || 'Chưa phân lớp';
        }

        // Load rankings when authenticated or allow guest access
        document.addEventListener('DOMContentLoaded', function() {
            onAuthStateChanged(auth, (user) => {
                if (user) {
                    console.log('User authenticated, loading rankings:', user.email);
                    loadRankings();
                } else {
                    console.log('No user authenticated, loading rankings as guest');
                    // For guest users, we'll need to handle this differently
                    // For now, show a message that login is required
                    loadRankings(); // Try anyway, might work with updated rules
                }
            });
        });
    </script>

    <script src="../assets/js/script.js"></script>
</body>
</html>