<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>B<PERSON><PERSON> Tập Code - Bài 3: <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON> v<PERSON> Chuỗi</title>
    <link rel="stylesheet" href="../../../assets/css/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .assignment-container {
            max-width: 900px;
            margin: 120px auto 50px;
            padding: 0 20px;
        }
        
        .assignment-header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
        }
        
        .assignment-header h1 {
            font-size: 2rem;
            margin-bottom: 15px;
        }
        
        .assignment-header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .code-info {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .code-info h2 {
            color: #4285F4;
            margin-bottom: 20px;
        }
        
        .code-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .stat-item {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .stat-item i {
            font-size: 2rem;
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .stat-item h3 {
            margin: 10px 0 5px 0;
            color: #333;
        }
        
        .stat-item p {
            margin: 0;
            color: #666;
            font-size: 0.9rem;
        }
        
        .start-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 50px;
            font-size: 1.2rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 20px 0;
        }
        
        .start-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }
        
        .back-link {
            display: inline-block;
            color: #4285F4;
            text-decoration: none;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .back-link:hover {
            text-decoration: underline;
        }
        
        .coming-soon {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 30px;
            text-align: center;
            margin: 30px 0;
        }
        
        .coming-soon h3 {
            color: #856404;
            margin-bottom: 15px;
        }
        
        .coming-soon p {
            color: #856404;
            margin: 0;
        }

        .exercise-preview {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
        }

        .exercise-preview h4 {
            color: #333;
            margin-bottom: 10px;
        }

        .exercise-preview p {
            color: #666;
            margin: 0;
            line-height: 1.6;
        }

        .exercises-container {
            margin-top: 30px;
        }

        .exercise-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 25px;
            overflow: hidden;
            transition: transform 0.3s ease;
        }

        .exercise-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .exercise-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .exercise-header h3 {
            margin: 0;
            font-size: 1.3rem;
        }

        .difficulty {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
        }

        .difficulty.easy {
            background: rgba(40, 167, 69, 0.2);
            color: #28a745;
            border: 1px solid #28a745;
        }

        .difficulty.medium {
            background: rgba(255, 193, 7, 0.2);
            color: #ffc107;
            border: 1px solid #ffc107;
        }

        .difficulty.hard {
            background: rgba(220, 53, 69, 0.2);
            color: #dc3545;
            border: 1px solid #dc3545;
        }

        .exercise-content {
            padding: 25px;
        }

        .exercise-content p {
            margin-bottom: 15px;
            line-height: 1.6;
        }

        .exercise-content ul {
            margin: 15px 0;
            padding-left: 25px;
        }

        .exercise-content li {
            margin-bottom: 8px;
            line-height: 1.6;
        }

        .output-section {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }

        .output-section h4 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 1rem;
        }

        .output-box {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 0.95rem;
            line-height: 1.4;
            margin-top: 10px;
        }

        .hint-section {
            margin-top: 15px;
            padding: 15px;
            background: #fff3cd;
            border-radius: 8px;
            border-left: 4px solid #ffc107;
        }

        .hint-section h4 {
            margin: 0 0 10px 0;
            color: #856404;
            font-size: 1rem;
        }

        .hint-section p {
            margin: 0;
            color: #856404;
            font-style: italic;
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <header>
        <div class="container">
            <div class="logo">
                <img src="../../../assets/images/logo.jpg" alt="VTA Logo">
            </div>
            <nav>
                <ul>
                    <li><a href="../../../index.html">Trang Chủ</a></li>
                    <li><a href="../../index.html">Lớp Học</a></li>
                    <li><a href="../../../achievements/">Thành Tích</a></li>
                    <li><a href="../../../auth/register.html">Đăng Ký</a></li>
                    <li><a href="../../../rankings/">Bảng Xếp Hạng</a></li>
                    <li><a href="../../../research/">Nghiên Cứu</a></li>
                    <li><a href="../../../auth/">Tài Khoản</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <div class="assignment-container">
        <a href="../../lessons/python-a/lesson-3.html" class="back-link">
            <i class="fas fa-arrow-left"></i> Quay lại Bài 3
        </a>

        <div class="assignment-header">
            <h1><i class="fas fa-code"></i> Bài Tập Luyện Tập</h1>
            <p>Bài 3: Dữ Liệu – Biến, Kiểu Số và Chuỗi</p>
        </div>

        <div class="code-info">
            <h2>17 Bài Tập Luyện Tập</h2>
            <div class="code-stats">
                <div class="stat-item">
                    <i class="fas fa-tasks"></i>
                    <h3>17 Bài Tập</h3>
                    <p>Luyện tập Python</p>
                </div>
                <div class="stat-item">
                    <i class="fas fa-graduation-cap"></i>
                    <h3>Tự Học</h3>
                    <p>Học theo tốc độ riêng</p>
                </div>
                <div class="stat-item">
                    <i class="fas fa-heart"></i>
                    <h3>Không Tính Điểm</h3>
                    <p>Chỉ để luyện tập</p>
                </div>
                <div class="stat-item">
                    <i class="fas fa-lightbulb"></i>
                    <h3>Có Gợi Ý</h3>
                    <p>Hướng dẫn chi tiết</p>
                </div>
            </div>

            <p style="text-align: center; color: #666; margin: 20px 0; font-style: italic;">
                💡 Các bài tập này giúp bạn luyện tập kiến thức về biến, số và chuỗi. Hãy thử làm và so sánh với output mong muốn!
            </p>

            <div style="text-align: center; margin: 30px 0;">
                <a href="lesson-3-exercises.html" class="start-button">
                    <i class="fas fa-play"></i> Xem 17 Bài Tập Luyện Tập
                </a>
            </div>
        </div>

        <!-- Exercise List -->
        <div class="exercises-container">
            <!-- Bài 1 -->
            <div class="exercise-card">
                <div class="exercise-header">
                    <h3><i class="fas fa-code"></i> Bài 1: Khai Báo Tuổi</h3>
                    <span class="difficulty easy">Dễ</span>
                </div>
                <div class="exercise-content">
                    <p><strong>Tên file:</strong> khai_bao_tuoi.py</p>
                    <p><strong>Yêu cầu:</strong> Tạo một biến tên là my_age và gán cho nó giá trị là tuổi hiện tại của bạn (một số nguyên). Sau đó, in giá trị của biến my_age ra màn hình.</p>
                    <div class="output-section">
                        <h4>Output Mẫu (Ví dụ nếu tuổi là 15):</h4>
                        <div class="output-box">15</div>
                    </div>
                </div>
            </div>

            <!-- Bài 2 -->
            <div class="exercise-card">
                <div class="exercise-header">
                    <h3><i class="fas fa-code"></i> Bài 2: Lời Chào Với Tên</h3>
                    <span class="difficulty easy">Dễ</span>
                </div>
                <div class="exercise-content">
                    <p><strong>Tên file:</strong> chao_ten.py</p>
                    <p><strong>Yêu cầu:</strong> Tạo một biến user_name và gán cho nó một tên bất kỳ (ví dụ: "Linh"). Sau đó, in ra màn hình lời chào: "Chao ban, [user_name]!" (thay [user_name] bằng giá trị của biến).</p>
                    <div class="output-section">
                        <h4>Output Mẫu (Ví dụ nếu user_name là "Linh"):</h4>
                        <div class="output-box">Chao ban, Linh!</div>
                    </div>
                </div>
            </div>

            <!-- Bài 3 -->
            <div class="exercise-card">
                <div class="exercise-header">
                    <h3><i class="fas fa-code"></i> Bài 3: Tính Diện Tích Hình Chữ Nhật Đơn Giản</h3>
                    <span class="difficulty easy">Dễ</span>
                </div>
                <div class="exercise-content">
                    <p><strong>Tên file:</strong> dien_tich_hcn.py</p>
                    <p><strong>Yêu cầu:</strong></p>
                    <ul>
                        <li>Tạo biến chieu_dai và gán giá trị là 10.</li>
                        <li>Tạo biến chieu_rong và gán giá trị là 5.</li>
                        <li>Tạo biến dien_tich và gán cho nó kết quả của chieu_dai nhân chieu_rong (dấu nhân: *).</li>
                        <li>In giá trị của biến dien_tich ra màn hình.</li>
                    </ul>
                    <div class="output-section">
                        <h4>Output Mẫu:</h4>
                        <div class="output-box">50</div>
                    </div>
                </div>
            </div>

            <!-- Bài 4 -->
            <div class="exercise-card">
                <div class="exercise-header">
                    <h3><i class="fas fa-code"></i> Bài 4: Lặp Lại Lời Cổ Vũ</h3>
                    <span class="difficulty easy">Dễ</span>
                </div>
                <div class="exercise-content">
                    <p><strong>Tên file:</strong> co_vu.py</p>
                    <p><strong>Yêu cầu:</strong> Tạo một biến slogan có giá trị là "Python Co Len! ". In biến slogan ra màn hình 3 lần liên tiếp trên cùng một dòng.</p>
                    <div class="output-section">
                        <h4>Output Mẫu:</h4>
                        <div class="output-box">Python Co Len! Python Co Len! Python Co Len! </div>
                    </div>
                </div>
            </div>

            <!-- Bài 5 -->
            <div class="exercise-card">
                <div class="exercise-header">
                    <h3><i class="fas fa-code"></i> Bài 5: Kiểm Tra Kiểu Dữ Liệu</h3>
                    <span class="difficulty easy">Dễ</span>
                </div>
                <div class="exercise-content">
                    <p><strong>Tên file:</strong> kiem_tra_kieu.py</p>
                    <p><strong>Yêu cầu:</strong></p>
                    <ul>
                        <li>Tạo biến var1 gán giá trị 100.</li>
                        <li>Tạo biến var2 gán giá trị 9.99.</li>
                        <li>Tạo biến var3 gán giá trị "Hello Python".</li>
                        <li>In ra kiểu dữ liệu của từng biến, mỗi kiểu trên một dòng.</li>
                    </ul>
                    <div class="output-section">
                        <h4>Output Mẫu:</h4>
                        <div class="output-box">&lt;class 'int'&gt;<br>&lt;class 'float'&gt;<br>&lt;class 'str'&gt;</div>
                    </div>
                </div>
            </div>

            <!-- Bài 6 -->
            <div class="exercise-card">
                <div class="exercise-header">
                    <h3><i class="fas fa-code"></i> Bài 6: Thông Tin Sản Phẩm</h3>
                    <span class="difficulty medium">Trung bình</span>
                </div>
                <div class="exercise-content">
                    <p><strong>Tên file:</strong> san_pham.py</p>
                    <p><strong>Yêu cầu:</strong></p>
                    <ul>
                        <li>Tạo biến ten_san_pham gán giá trị "Ao Thun Python".</li>
                        <li>Tạo biến gia_ban gán giá trị 250.0 (số thực).</li>
                        <li>Tạo biến so_luong_ton_kho gán giá trị 50.</li>
                        <li>In thông tin sản phẩm ra màn hình theo định dạng sau:</li>
                    </ul>
                    <div class="output-section">
                        <h4>Output Mẫu:</h4>
                        <div class="output-box">Ten san pham: Ao Thun Python<br>Gia ban: 250.0 VND<br>So luong ton kho: 50</div>
                    </div>
                    <div class="hint-section">
                        <h4>💡 Gợi ý:</h4>
                        <p>Nếu chỉ dùng print và nối chuỗi, bạn cần in gia_ban và so_luong_ton_kho riêng hoặc tìm cách nối chúng với chuỗi một cách khéo léo, ví dụ print("Gia ban: ", gia_ban, " VND") nếu đã học print nhiều tham số.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section footer-logo">
                    <div class="footer-logo-container">
                        <img src="../../../assets/images/logo.jpg" alt="VTA Logo">
                        <div class="logo-text">Vthon Academy</div>
                    </div>
                    <div class="slogan">Học, học nữa, học mãi.</div>
                    <div class="footer-bottom">
                        <p>&copy; 2025 – All rights reserved.</p>
                    </div>
                </div>

                <div class="footer-section footer-contact">
                    <h3>Liên hệ</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> 0399787678</p>
                    <p><i class="fab fa-facebook-messenger"></i> Zalo: 0399787678</p>
                </div>

                <div class="footer-section footer-social">
                    <h3>Mạng xã hội</h3>
                    <div class="social-links">
                        <a href="https://www.facebook.com/vinhle030904/" target="_blank" class="social-link facebook">
                            <i class="fab fa-facebook-f"></i> Facebook
                        </a>
                        <a href="https://www.tiktok.com/@sunnii39" target="_blank" class="social-link tiktok">
                            <i class="fab fa-tiktok"></i> TikTok
                        </a>
                        <a href="https://www.instagram.com/vlee.39/?hl=en" target="_blank" class="social-link instagram">
                            <i class="fab fa-instagram"></i> Instagram
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script>
        function startCoding() {
            // This will be implemented when coding assignment is ready
            alert('Bài tập sẽ sớm được cập nhật!');
        }
    </script>
    <script src="../../../assets/js/script.js"></script>
</body>
</html>
