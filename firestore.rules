rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Allow users to read and write their own profile data and assignments
    // Allow anyone (including guests) to read users data for public features like rankings and class listings
    // Allow admin to update any user for badge management
    match /users/{userId} {
      allow read: if true;
      allow write: if request.auth != null &&
        (request.auth.uid == userId || request.auth.token.email == '<EMAIL>');

      // Allow users to read their own assignments, create new ones, but not update existing ones
      // <PERSON><PERSON> can read all assignments
      match /assignments/{assignmentId} {
        allow read: if request.auth != null &&
          (request.auth.uid == userId || request.auth.token.email == '<EMAIL>');
        allow create: if request.auth != null && request.auth.uid == userId;
        // Prevent updates to existing assignments to avoid duplicate scoring
        allow update: if request.auth != null && request.auth.token.email == '<EMAIL>';
      }

      // Allow users to read and write their own coding assignments
      // Admin can read all coding assignments
      match /coding-assignments/{assignmentId} {
        allow read: if request.auth != null &&
          (request.auth.uid == userId || request.auth.token.email == '<EMAIL>');
        allow create, update: if request.auth != null && request.auth.uid == userId;
      }
    }
    
    // Allow authenticated users to read and write assignment submissions
    match /assignments/{assignmentId} {
      allow read, write: if request.auth != null;
    }
    
    // Allow authenticated users to read and write their own assignment submissions
    match /assignment_submissions/{submissionId} {
      allow read, write: if request.auth != null;
    }
    
    // Allow authenticated users to read class data
    match /classes/{classId} {
      allow read: if request.auth != null;
    }
    
    // Allow authenticated users to read and write rankings data
    match /rankings/{rankingId} {
      allow read, write: if request.auth != null;
    }
    
    // Allow anyone to read and write registration data (for public registration form)
    match /registrations/{registrationId} {
      allow read, write: if true;
    }
    
    // Admin-only access for user management (only for admin email)
    match /admin/{document=**} {
      allow read, write: if request.auth != null && request.auth.token.email == '<EMAIL>';
    }

    // Class settings - admin can read/write, users can only read
    match /classSettings/{classId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.token.email == '<EMAIL>';
    }

    // Fee payments - admin only
    match /feePayments/{paymentId} {
      allow read, write: if request.auth != null && request.auth.token.email == '<EMAIL>';
    }
    
    // Allow anyone to read achievements data, authenticated users can write (for likes/views)
    match /achievements/{achievementId} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // Allow authenticated users to read research data
    match /research/{researchId} {
      allow read: if request.auth != null;
    }

    // Messages collection - users can read their own messages, admin can read/write all
    match /messages/{messageId} {
      allow read: if request.auth != null &&
        (resource.data.recipientId == request.auth.uid ||
         request.auth.token.email == '<EMAIL>');
      allow write: if request.auth != null && request.auth.token.email == '<EMAIL>';
      allow update: if request.auth != null &&
        (resource.data.recipientId == request.auth.uid ||
         request.auth.token.email == '<EMAIL>');
    }

    // Badges collection - anyone can read, only admin can write
    match /badges/{badgeId} {
      allow read: if true;
      allow write: if request.auth != null && request.auth.token.email == '<EMAIL>';
    }

    // Notifications collection - users can read their own notifications, admin can read/write all
    match /notifications/{notificationId} {
      allow read: if request.auth != null &&
        (resource.data.userId == request.auth.uid ||
         request.auth.token.email == '<EMAIL>');
      allow write: if request.auth != null && request.auth.token.email == '<EMAIL>';
    }
  }
}
